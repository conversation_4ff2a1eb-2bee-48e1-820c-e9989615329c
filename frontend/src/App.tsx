import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, App as AntApp, theme } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { QueryClient, QueryClientProvider } from 'react-query';
import { useAuthStore } from '@/store/auth';
import Layout from '@/components/Layout';
import Login from '@/pages/Login';
import Dashboard from '@/pages/Dashboard';
import Projects from '@/pages/Projects';
import Parts from '@/pages/Parts';
import Machines from '@/pages/Machines';
import WorkOrders from '@/pages/WorkOrders';
import PlanTasks from '@/pages/PlanTasks';
import Execution from '@/pages/Execution';
import Quality from '@/pages/Quality';
import Users from '@/pages/Users';
import RolePermissions from '@/pages/RolePermissions';
import PermissionConfig from '@/pages/PermissionConfig';
import PermissionConfigSimple from '@/pages/PermissionConfigSimple';
import BOM from '@/pages/BOM';
import Routings from '@/pages/Routings';
import ApiTest from '@/pages/ApiTest';
import DatabaseViewer from '@/pages/DatabaseViewer';
import ApiDebug from '@/pages/ApiDebug';
import OperatorExecution from '@/pages/OperatorExecution';
import PartSelectionDemo from '@/pages/PartSelectionDemo';
import SystemConfig from '@/pages/SystemConfig';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorBoundary from '@/components/ErrorBoundary';
import './App.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuthStore();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

const App: React.FC = () => {
  const { isAuthenticated, getCurrentUser, isLoading } = useAuthStore();

  useEffect(() => {
    // Try to get current user on app start if token exists
    const token = localStorage.getItem('token');
    if (token && !isAuthenticated) {
      getCurrentUser();
    }
  }, [isAuthenticated, getCurrentUser]);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ConfigProvider
          locale={zhCN}
          theme={{
            algorithm: theme.defaultAlgorithm,
            token: {
              colorPrimary: '#1890ff',
              borderRadius: 6,
            },
          }}
        >
          <AntApp>
            <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
              <Routes>
                <Route path="/login" element={<Login />} />
                <Route
                  path="/*"
                  element={
                    <ProtectedRoute>
                      <Layout>
                        <ErrorBoundary>
                          <Routes>
                            <Route path="/" element={<Navigate to="/dashboard" replace />} />
                            <Route path="/dashboard" element={<Dashboard />} />
                            <Route path="/projects" element={<Projects />} />
                            <Route path="/parts" element={<Parts />} />
                            <Route path="/machines" element={<Machines />} />
                            <Route path="/work-orders" element={<WorkOrders />} />
                            <Route path="/plan-tasks" element={<PlanTasks />} />
                            <Route path="/execution" element={<Execution />} />
                            <Route path="/operator-execution" element={<OperatorExecution />} />
                            <Route path="/quality" element={<Quality />} />
                            <Route path="/bom" element={<BOM />} />
                            <Route path="/routings" element={<Routings />} />
                            <Route path="/users" element={<Users />} />
                            <Route path="/role-permissions" element={<RolePermissions />} />
                            <Route path="/permission-config" element={<PermissionConfig />} />
                            <Route path="/permission-config-simple" element={<PermissionConfigSimple />} />
                            <Route path="/system-config" element={<SystemConfig />} />
                            <Route path="/api-test" element={<ApiTest />} />
                            <Route path="/database" element={<DatabaseViewer />} />
                            <Route path="/api-debug" element={<ApiDebug />} />
                            <Route path="/part-selection-demo" element={<PartSelectionDemo />} />
                          </Routes>
                        </ErrorBoundary>
                      </Layout>
                    </ProtectedRoute>
                  }
                />
              </Routes>
            </Router>
          </AntApp>
        </ConfigProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;
